// Generated by nuxi
{
  "compilerOptions": {
    "jsx": "preserve",
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "Node",
    "skipLibCheck": true,
    "strict": false,
    "allowJs": true,
    "noEmit": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "types": [
      "node"
    ],
    "baseUrl": "..",
    "paths": {
      "~~": [
        "."
      ],
      "~~/*": [
        "./*"
      ],
      "@@": [
        "."
      ],
      "@@/*": [
        "./*"
      ],
      "~": [
        "."
      ],
      "~/*": [
        "./*"
      ],
      "@": [
        "."
      ],
      "@/*": [
        "./*"
      ],
      "assets": [
        "assets"
      ],
      "assets/*": [
        "assets/*"
      ],
      "public": [
        "public"
      ],
      "public/*": [
        "public/*"
      ],
      "#app": [
        "node_modules/nuxt/dist/app"
      ],
      "#app/*": [
        "node_modules/nuxt/dist/app/*"
      ],
      "vue-demi": [
        "node_modules/nuxt/dist/app/compat/vue-demi"
      ],
      "#head": [
        "node_modules/nuxt/dist/head/runtime"
      ],
      "#head/*": [
        "node_modules/nuxt/dist/head/runtime/*"
      ],
      "#imports": [
        ".nuxt/imports"
      ],
      "#build": [
        ".nuxt"
      ],
      "#build/*": [
        ".nuxt/*"
      ],
      "#components": [
        ".nuxt/components"
      ]
    }
  },
  "include": [
    "./nuxt.d.ts",
    "../**/*"
  ]
}
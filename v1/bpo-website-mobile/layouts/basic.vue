<template lang="pug">
.basic-layout
  slot
</template>

<script lang="ts" setup>
import wx from 'weixin-js-sdk'
import { onMounted } from 'vue'
import axios from 'axios';

useHead({
  meta: [{
    name: 'viewport',
    content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=yes'
  }],
  script: [
    {
      src: 'https://hm.baidu.com/hm.js?fc780d26a7f36d192d4d85843f67437e'
    }, {
      src: 'https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/1.1.0-beta.0/libs/cn/index.js'
    }
  ],
  link: [
    {
      rel: 'stylesheet',
      href: 'https://cdnjs.cloudflare.com/ajax/libs/video.js/8.21.1/video-js.min.css'
    }
  ]
})

// 判断是不是微信环境
const isWeChat = () => {
  const navigator = window.navigator;

  if (!navigator?.userAgent) return false;

  const ua: any = navigator?.userAgent.toLowerCase();

  return ua.match(/MicroMessenger/i) == 'micromessenger';
};

onMounted(() => {
  if (!isWeChat()) return

  axios.post('https://www.smartdeer.com/api/v1/wx/utils/js/sdk/sign', {
    url: window.location.href
  }).then((res) => {
    const info = res.data.data
    wx.config({
      debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
      appId: info.appId, // 必填，公众号的唯一标识
      timestamp: info.timestamp, // 必填，生成签名的时间戳
      nonceStr: info.nonceStr, // 必填，生成签名的随机串
      signature: info.signature, // 必填，签名
      jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'], // 必填，需要使用的JS接口列表
    });


    const pathname = window.location.pathname
    const systemLang = pathname.split('/')[1]

    const title = systemLang === 'zh' ? document.title + ' - SmartDeer - 全球雇 & 全球招' : document.title + ' - SmartDeer'
    const desc = systemLang === 'zh' ? '全球雇 & 全球招，出海企业的一站式解决方案专家' : 'Global Employment  & Recruitment'
    const link = window.location.href
    const imgUrl = 'https://global-image.smartdeer.work/p/images/0x586db870d27b4a0b8764d9992ed81590.png'

    wx.ready(function () {   //需在用户可能点击分享按钮前就先调用
      wx.updateAppMessageShareData({ 
        title, // 分享标题
        desc, // 分享描述
        link, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
        imgUrl, // 分享图标
        success: function () {
          // 设置成功
        }
      })

      wx.updateTimelineShareData({ 
        title, // 分享标题
        link, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
        imgUrl, // 分享图标
        success: function () {
          // 设置成功
        }
      })
    });
  })

})
</script>

<style lang="scss" scoped>
.basic-layout {
  --max-width: 680px;
}
</style>
<template lang="pug">
.countries-page
  site-header(lang="en" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")

    .countries-content
      h1.article-title 中东搞钱到底去哪个城市？
      div(v-html="htmlContent")
  site-footer(lang="en" @contact-us="status.showForm = true")
  .contact-form
    client-only
      el-dialog(v-model="status.showForm" title="" :width="354")
        contact-us-form(lang="en" @submit="submitSuccess")
  .fixed-contact(@click="status.showForm = true")
    img(src="~/assets/images/aboutus/ic_contact_us_en.png")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({ layout: 'basic' })
useSeoMeta({
  ogTitle: '中东搞钱到底去哪个城市？',
  ogDescription: '中东搞钱到底去哪个城市呢？目前经商环境成熟，有政策红利，公认有潜力的市场，基本就是阿联酋和沙特。沙特的航运、贸 […]',
  ogSiteName: 'SmartDeer',
  description: '中东搞钱到底去哪个城市呢？目前经商环境成熟，有政策红利，公认有潜力的市场，基本就是阿联酋和沙特。沙特的航运、贸 […]'
})
useHead({
  htmlAttrs: {
    lang: 'en-US'
  },
  title: '中东搞钱到底去哪个城市？'
})

const status = reactive({
  showForm: false
})

const pageTitle = '中东搞钱到底去哪个城市？';
const bannerImage = '';
const flagImage = '';
const htmlContent = '<p>中东搞钱到底去哪个城市呢？目前经商环境成熟，有政策红利，公认有潜力的市场，基本就是阿联酋和沙特。沙特的航运、贸易、金融、物流在蓬勃发展中，在迪拜也可以看到来自中国的各种各样的商品在这里进场了，也是中国产业出海的首选。另外阿联酋整个国家在新能源方面，投入巨大，除了大家所熟知的太阳能，近年来还加大了对海上风能、氢能的投入，新能源汽车也搞得热火朝天，据说小米汽车的组装也将落地阿联酋。沙特就是有很多以政府为主导的项目，他们不止投钱，还算望中国企业去沙特本土建厂，参与以国家主导的基础建设类工程项目，总的来说，沙特就是在一个改革开放的过程，另外呢，以零售电商为主导的消费量也有很大的潜力，现在大家都在说国内很卷，代表了各个产业的一个产能过剩，所以不得不加速了大家出海的一个速度，不出海可能就真的活不下去了，我们SmartDeer 及合作伙伴提供150多个国家和地区的一站式人力资源雇佣方案，可以有效减轻出海中东的门槛和风险，可以增加成功出海中东的可能性，如果您的企业有出海计划，欢迎在评论区留下你的行业。</p>';

function submitSuccess() {
  ElMessage.success('We have received your request and we will contact with you as soon as possible.')
  status.showForm = false
}

function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>

<style lang="scss">
@import './article-detail.scss';
</style>
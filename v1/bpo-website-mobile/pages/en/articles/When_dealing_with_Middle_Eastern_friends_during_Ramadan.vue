<template lang="pug">
.countries-page
  site-header(lang="en" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")

    .countries-content
      h1.article-title 斋月期间和中东小伙伴打交道，get这4个重点
      div(v-html="htmlContent")
  site-footer(lang="en" @contact-us="status.showForm = true")
  .contact-form
    client-only
      el-dialog(v-model="status.showForm" title="" :width="354")
        contact-us-form(lang="en" @submit="submitSuccess")
  .fixed-contact(@click="status.showForm = true")
    img(src="~/assets/images/aboutus/ic_contact_us_en.png")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({ layout: 'basic' })
useSeoMeta({
  ogTitle: '斋月期间和中东小伙伴打交道，get这4个重点',
  ogDescription: '这四点重要的斋月商务指南，出海的中国企业一定一定要看完，否则踩坑了都不知道。第一，灵活的工作时间。在沙特和阿联 […]',
  ogSiteName: 'SmartDeer',
  description: '这四点重要的斋月商务指南，出海的中国企业一定一定要看完，否则踩坑了都不知道。第一，灵活的工作时间。在沙特和阿联 […]'
})
useHead({
  htmlAttrs: {
    lang: 'en-US'
  },
  title: '斋月期间和中东小伙伴打交道，get这4个重点'
})

const status = reactive({
  showForm: false
})

const pageTitle = '斋月期间和中东小伙伴打交道，get这4个重点';
const bannerImage = '';
const flagImage = '';
const htmlContent = '<p>这四点重要的斋月商务指南，出海的中国企业一定一定要看完，否则踩坑了都不知道。第一，灵活的工作时间。在沙特和阿联酋，斋月期间工作时间一般会缩短两个小时，再加上本土员工他们没吃饭没喝水，这样子的斋戒的状态，整体的工作效率可能会下降30%~50%，所以在这个月，就要灵活的安排工作时间和任务安排。第二，尊重斋戒习俗。斋戒期间白天在公共场合吃东西、喝水或者说吸烟都是不被允许的，在沙特的这种规定尤为严格，即使你不斋戒，你应该避免在公共场合以及穆斯林同事面前公开的这种吃喝。第三，祈祷时间。沙特同事他们有5次的祈祷时间，尤其是在斋月期间，我们尽量不要在这些时间来安排会议，了解并尊重这些祈祷的时间，也是有利于大家规划一个高效的工作日。第四，交通安全提醒。往年斋月期间交通事故会显著增加，毕竟节食这个行为，让身体处于更容易疲劳，对司机开车的时候的注意力，以及反应的速度有很大影响，在出行的高峰期会导致事故频发，所以大家开车的时候一定要保持注意力，尽量去避开日落前的出行高峰。尊重当地文化习俗，不仅能更好的在当地市场进行商业活动，还能避免很多文化习俗上的坑，我们smartdeer拥有全球150多个国家和地区的用工经验，并且对沙特这些宗教国家的信仰也有足够多的了解，帮数十个企业避过坑，节约罚款和其他法律费用上亿元，想了解更多关于出海沙特的信息，也可以在评论区留言。</p>';

function submitSuccess() {
  ElMessage.success('We have received your request and we will contact with you as soon as possible.')
  status.showForm = false
}

function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>

<style lang="scss">
@import './article-detail.scss';
</style>
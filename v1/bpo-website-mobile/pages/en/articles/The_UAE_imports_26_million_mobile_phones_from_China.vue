<template lang="pug">
.countries-page
  site-header(lang="en" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")

    .countries-content
      h1.article-title 阿联酋总共才1100万人，却从中国进口的2600万部手机！
      div(v-html="htmlContent")
  site-footer(lang="en" @contact-us="status.showForm = true")
  .contact-form
    client-only
      el-dialog(v-model="status.showForm" title="" :width="354")
        contact-us-form(lang="en" @submit="submitSuccess")
  .fixed-contact(@click="status.showForm = true")
    img(src="~/assets/images/aboutus/ic_contact_us_en.png")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({ layout: 'basic' })
useSeoMeta({
  ogTitle: '阿联酋总共才1100万人，却从中国进口的2600万部手机！',
  ogDescription: '阿联酋总共才1100万人，但仅2023年一年，他们就从中国进口的2600万部手机！他们进口这么多手机究竟是做什 […]',
  ogSiteName: 'SmartDeer',
  description: '阿联酋总共才1100万人，但仅2023年一年，他们就从中国进口的2600万部手机！他们进口这么多手机究竟是做什 […]'
})
useHead({
  htmlAttrs: {
    lang: 'en-US'
  },
  title: '阿联酋总共才1100万人，却从中国进口的2600万部手机！'
})

const status = reactive({
  showForm: false
})

const pageTitle = '阿联酋总共才1100万人，却从中国进口的2600万部手机！';
const bannerImage = '';
const flagImage = '';
const htmlContent = '<p>阿联酋总共才1100万人，但仅2023年一年，他们就从中国进口的2600万部手机！他们进口这么多手机究竟是做什么用呢？中东国家普遍富有，也造就了这里民众的一个习惯，也就是高消费，尤其是对数码产品情有独钟。2022年，阿联酋的人均GDP高达5万美元，比英国的4.8万美元还要高出一些。这样一来，也有了一个现象，就是阿联酋的人们特别喜欢买电子产品，很多人旧款还没用很久就会迫不及待买入新款。因此阿联酋的人们换手机频率十分之高，而且在他们眼中，小米、华为这些来自中国的品牌不仅性能好，外观也十分漂亮。当然，2600万台手机已经是人口的两倍之多，他们也用不完，那为何还要进口如此之多呢？这就不得不说阿联酋的另一门生意了，也就是自由出口贸易，就是先从别的国家进回来，再加价卖出去。其实除了手机，阿联酋还会购买很多我国的产品，比如家电、纺织品、新能源汽车等等。同时我国也与其有着贸易往来，比如最重要的石油。简单来说，这就是阿联酋市场庞大的一个缩影，现在很多企业纷纷出海中东，你还不着急吗？我们smartdeer给不少出海中东的企业提供全球招聘团队、海外招聘平台、海外校园招聘，帮助十多个企业在阿联酋快速搭建本地化团队，如果您的企业也想出海，留言出海，给您一份国家手册，让您比同行更快一步提前了解出海讯息。</p>';

function submitSuccess() {
  ElMessage.success('We have received your request and we will contact with you as soon as possible.')
  status.showForm = false
}

function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>

<style lang="scss">
@import './article-detail.scss';
</style>
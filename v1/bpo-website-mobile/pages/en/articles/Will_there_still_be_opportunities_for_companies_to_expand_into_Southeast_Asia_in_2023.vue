<template lang="pug">
.countries-page
  site-header(lang="en" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")

    .countries-content
      h1.article-title 2023年企业出海东南亚还有机会吗？
      div(v-html="htmlContent")
  site-footer(lang="en" @contact-us="status.showForm = true")
  .contact-form
    client-only
      el-dialog(v-model="status.showForm" title="" :width="354")
        contact-us-form(lang="en" @submit="submitSuccess")
  .fixed-contact(@click="status.showForm = true")
    img(src="~/assets/images/aboutus/ic_contact_us_en.png")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({ layout: 'basic' })
useSeoMeta({
  ogTitle: '2023年企业出海东南亚还有机会吗？',
  ogDescription: '现在出海东南亚时机刚刚好，有下面几点，首先，国内的制造业内卷是非常严重的，包括销售渠道的内卷，无论从电商渠道， […]',
  ogSiteName: 'SmartDeer',
  description: '现在出海东南亚时机刚刚好，有下面几点，首先，国内的制造业内卷是非常严重的，包括销售渠道的内卷，无论从电商渠道， […]'
})
useHead({
  htmlAttrs: {
    lang: 'en-US'
  },
  title: '2023年企业出海东南亚还有机会吗？'
})

const status = reactive({
  showForm: false
})

const pageTitle = '2023年企业出海东南亚还有机会吗？';
const bannerImage = '';
const flagImage = '';
const htmlContent = '<p>现在出海东南亚时机刚刚好，有下面几点，首先，国内的制造业内卷是非常严重的，包括销售渠道的内卷，无论从电商渠道，再到现在的直播带货，所有的渠道都大大的写着两个字，竞争。现在的话，中国的所有的制造业都想的办法去出海，像江浙、广州、深圳都是由政府部门带队到海外进行拿订单。第二点，从宏观政策我们来看一下，我们牵头了一带一路协议，经过整个东南亚，然后我们加入了东盟的RCEP协议，也为我们制造业出海东南亚，铺垫了一个政策上面的大的利好方向。第三点，我要讲的是中国制造商品的这个优势，那么作为有世界工厂支撑的中国大陆，在全世界任何一个其他国家来说，中国制造在95%以上的类目都是有价格优势的，同样的质量，我们的成本更低，否则不可能全世界这么多国家都疯狂的在中国进货。最后一点是企业出海东南亚本土的优势，如果你是通过出口公司来出口，中间环节太多，利润层层叠加，那么如果是你企业可以去掉这些环节，直接在东南亚建公司的话，没有这个中间的环节，厂家的价格和质量的优势会更明显，我们smartdeer给不少出海东南亚的企业提供了全球招聘团队，帮助十多个企业在东南亚快速搭建本地化团队，企业出海东南亚的运营成本也能降低很多，我们还有一份国家手册给到您，需要的话请留言。</p>';

function submitSuccess() {
  ElMessage.success('We have received your request and we will contact with you as soon as possible.')
  status.showForm = false
}

function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>

<style lang="scss">
@import './article-detail.scss';
</style>
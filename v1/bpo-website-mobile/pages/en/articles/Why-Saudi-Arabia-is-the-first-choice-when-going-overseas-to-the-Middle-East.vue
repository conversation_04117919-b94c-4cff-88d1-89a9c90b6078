<template lang="pug">
.countries-page
  site-header(lang="en" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")

    .countries-content
      h1.article-title 企业出海中东市场，为什么首选沙特
      div(v-html="htmlContent")
  site-footer(lang="en" @contact-us="status.showForm = true")
  .contact-form
    client-only
      el-dialog(v-model="status.showForm" title="" :width="354")
        contact-us-form(lang="en" @submit="submitSuccess")
  .fixed-contact(@click="status.showForm = true")
    img(src="~/assets/images/aboutus/ic_contact_us_en.png")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({ layout: 'basic' })
useSeoMeta({
  ogTitle: '企业出海中东市场，为什么首选沙特',
  ogDescription: '企业出海中东为什么要首选沙特？不明白这背后的四大原因，盲目行动只会人财两空。我们smartdeer就见过很多案 […]',
  ogSiteName: 'SmartDeer',
  description: '企业出海中东为什么要首选沙特？不明白这背后的四大原因，盲目行动只会人财两空。我们smartdeer就见过很多案 […]'
})
useHead({
  htmlAttrs: {
    lang: 'en-US'
  },
  title: '企业出海中东市场，为什么首选沙特'
})

const status = reactive({
  showForm: false
})

const pageTitle = '企业出海中东市场，为什么首选沙特';
const bannerImage = '';
const flagImage = '';
const htmlContent = '<p>企业出海中东为什么要首选沙特？不明白这背后的四大原因，盲目行动只会人财两空。我们smartdeer就见过很多案例，没有明确规划就出海，后果不堪设想。首先，沙特作为海湾地区最大的经济体，市场规模和潜力非常大。其次，沙特正处于革新期，社会越来越开放，营商环境友好，为企业投资和发展提供了良好的外部环境。此外，沙特拥有丰富的自然资源和良好的基础设施，如石油、天然气等，为企业的生产经营提供了有力的支持。最后，近年来，中沙经贸合作不断扩大，两国之间的经贸关系日益紧密，为企业出海沙特提供了良好的合作基础和机遇。沙特的人口也超过了海湾地区总人口的60%，这为企业提供了广阔的市场空间和消费者基础。我们smartdeer在全球服务了150多个国家和地区，其中不乏沙特，为出海沙特的企业提供全球招全球雇，帮他们避过很多坑。所以沙特不仅有巨大的市场潜力，还有友好的营商环境、丰富的资源和完善的基础设施，现在中沙经贸合作日益紧密，出海沙特是非常明智的选择。</p>';

function submitSuccess() {
  ElMessage.success('We have received your request and we will contact with you as soon as possible.')
  status.showForm = false
}

function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>

<style lang="scss">
@import './article-detail.scss';
</style>
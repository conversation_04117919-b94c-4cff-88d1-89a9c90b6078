<template lang="pug">
section.site-footer
  h2 footer
  .site-footer-container(v-if="lang === 'zh'")
    .logo
      .footer-logo-wrap
        .footer-logo
          figure
            img(src="~/assets/images/index/footer-logo.png")

        .email(@click="handleMailsome") <EMAIL>
      .footer-logo-cert
        img(src="~/assets/images/index/iso27001.png")
    .social
      .text 加入社区
      .imgs
        figure(@click="handleClickTo('https://twitter.com/SmartDeerGlobal')")
          img(src="~/assets/images/index/twitter.svg")
        figure(@click="handleClickTo('https://t.me/SmartDeerGlobal')")
          img(src="~/assets/images/index/telegram.svg")
        figure(@click="handleClickTo('https://www.linkedin.com/company/smartdeer-global/career/')")
          img(src="~/assets/images/index/LinkedIn.svg")
    .slogon
      span(@click="handleClickTo('https://www.smartdeer.com/')") 灵鹿聘·全球人才招聘
      a(href="/zh/aboutus") 关于我们
      span(@click="()=>{$emit('contact-us')}") 联系我们
    
    .spliter

    .compliance
      .content-zh
        span © 2022 SMARTDEER<br/>
        span 北京谊桥管理科技有限责任公司版权所有
        span 京ICP备**********号-1
        NuxtLink(to="/compliance/hr-service-permit") 人力资源许可证
        NuxtLink(to="/compliance/business-license") 营业执照
      client-only
        el-dropdown.language-selector
          .text
            div 中文
            figure
              img(src="~/assets/images/index/trangle.svg")
          template(#dropdown)
            el-dropdown-menu
              el-dropdown-item(@click="switchLang('zh')") 中文
              el-dropdown-item(@click="switchLang('en')") English
              el-dropdown-item(@click="switchLang('ja')") 日本語

  .site-footer-container.helvetica(v-if="lang === 'en'")
    .logo
      .footer-logo-wrap
        .footer-logo
          figure
            img(src="~/assets/images/index/footer-logo.png")

        .email(@click="handleMailsome") <EMAIL>
      .footer-logo-cert
        img(src="~/assets/images/index/iso27001.png")

    .social
      .text Join the community
      .imgs
        figure(@click="handleClickTo('https://twitter.com/SmartDeerGlobal')")
          img(src="~/assets/images/index/twitter.svg")
        figure(@click="handleClickTo('https://t.me/SmartDeerGlobal')")
          img(src="~/assets/images/index/telegram.svg")
        figure(@click="handleClickTo('https://www.linkedin.com/company/smartdeer-global/career/')")
          img(src="~/assets/images/index/LinkedIn.svg")

    .slogon
      span(@click="handleClickTo('https://www.smartdeer.com/')") Smartdeer Global Recruitment
      a(href="/en/aboutus") About Us
      span(@click="()=>{$emit('contact-us')}") Contact Us

    .spliter

    .compliance
      .content-en
        span(style="margin-right: 5px") Copyright©  2022 
        span(style="margin-right: 5px") Beijing Yiqiao LLC All Rights Reserved    
        NuxtLink(to="/compliance/hr-service-permit" style="margin-right: 5px") Human Resources License   
        NuxtLink(to="/compliance/business-license") Business license
      client-only
        el-dropdown.language-selector
          .text
            div English
            figure
              img(src="~/assets/images/index/trangle.svg")
          template(#dropdown)
            el-dropdown-menu
              el-dropdown-item(@click="switchLang('zh')") 中文
              el-dropdown-item(@click="switchLang('en')") English
              el-dropdown-item(@click="switchLang('ja')") 日本語

  .site-footer-container.helvetica(v-if="lang === 'ja'")
    .logo
      .footer-logo-wrap
        .footer-logo
          figure
            img(src="~/assets/images/index/footer-logo.png")
        .email(@click="handleMailsome") <EMAIL>
      .footer-logo-cert
        img(src="~/assets/images/index/iso27001.png")
    .social
      .text コミュニティに参加
      .imgs
        figure(@click="handleClickTo('https://twitter.com/SmartDeerGlobal')")
          img(src="~/assets/images/index/twitter.svg")
        figure(@click="handleClickTo('https://t.me/SmartDeerGlobal')")
          img(src="~/assets/images/index/telegram.svg")
        figure(@click="handleClickTo('https://www.linkedin.com/company/smartdeer-global/career/')")
          img(src="~/assets/images/index/LinkedIn.svg")
    .slogon
      span(@click="handleClickTo('https://www.smartdeer.com/')") SmartDeer グローバル採用
      a(href="/ja/aboutus") 会社概要
      span(@click="()=>{$emit('contact-us')}") お問い合わせ
    
    .spliter
    
    .compliance
      .content-ja
        span © 2022 SMARTDEER<br/>
        span 北京谊桥管理科技有限责任公司版权所有
        NuxtLink(to="/compliance/hr-service-permit") 人材採用許可証
        NuxtLink(to="/compliance/business-license") 事業許可証
      client-only
        el-dropdown.language-selector
          .text
            div 日本語
            figure
              img(src="~/assets/images/index/trangle.svg")
          template(#dropdown)
            el-dropdown-menu
              el-dropdown-item(@click="switchLang('zh')") 中文
              el-dropdown-item(@click="switchLang('en')") English
              el-dropdown-item(@click="switchLang('ja')") 日本語

  .overlay(v-show="showDialog")
    .dialog(v-if="lang == 'zh'")
      .dialog-box
        .title 灵鹿聘·全球人才招聘
          figure.dialog-close(@click="showDialog = false")
            img(src="~/assets/images/index/close.svg")
        .content 
          .col
            .col-title 全球社招
            .img-box
              figure.wechat-qr
                img(src="~/assets/images/index/wechat-qr.jpg")
              .text 请使用微信扫描二维码

          .col
            .col-title 全球校招
            .img-box(@click="handleClickTo('https://m.smartdeer.co')")
              figure.guide-pc
                img(src="~/assets/images/index/guide-pc.svg")
              .link https://m.smartdeer.co
      
    .dialog(v-if="lang == 'en'")
      .dialog-box
        .title Smartdeer Global Recruitment
          figure.dialog-close(@click="showDialog = false")
            img(src="~/assets/images/index/close.svg")
        .content 
          .col
            .col-title For White Collar Recruitment
            .img-box
              figure.wechat-qr
                img(src="~/assets/images/index/wechat-qr.jpg")
              .text Please use WeChat to scan the QR code

          .col
            .col-title For Student Recruitment
            .img-box(@click="handleClickTo('https://m.smartdeer.co')")
              figure.guide-pc
                img(src="~/assets/images/index/guide-pc.svg")
              .link https://m.smartdeer.co

    .dialog(v-if="lang == 'ja'")
      .dialog-box
        .title グローバル人材採用
          figure.dialog-close(@click="showDialog = false")
            img(src="~/assets/images/index/close.svg")
        .content 
          .col
            .col-title グローバル社招
            .img-box
              figure.wechat-qr
                img(src="~/assets/images/index/wechat-qr.jpg")
              .text ウェブサイトを開いてください
          
          .col
            .col-title グローバル校招
            .img-box(@click="handleClickTo('https://m.smartdeer.co')")
              figure.guide-pc
                img(src="~/assets/images/index/guide-pc.svg")
              .link https://m.smartdeer.co

</template>
  
<script lang="ts" setup>
import { ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import langTool from '~/assets/utils/lang'
const props = defineProps(['lang'])
const { lang } = toRefs(props)
const showDialog = ref(false)

const handleClickTo = (url) => {
  // window.open('https://m.smartdeer.co')
  window.open(url)
}

function switchLang(lang) {
  const pathName = window.location.pathname
  const newPathName = pathName.substring(3)

  langTool.swithLang(lang, newPathName)
}

const handleMailsome = () => {
  parent.location.href = 'mailto:<EMAIL>';
}
</script>
  
<style lang="scss" scoped>
@import url('@/assets/styles/en.scss');

.helvetica{
  font-family: Helvetica;

  .wechat-name {
    width: 140px !important;
  }

  .usage {
    width: 121px !important;
    text-align: center;
    margin: 0 auto;
  }
}

.site-footer {
  background-color: #272A30;
  padding: 16px 0 24px 0;
  min-width: 375px;

  h2 {
    display: none;
  }

  .site-footer-container {
    padding: 0 20px;
    max-width: var(--max-width);
    box-sizing: border-box;
    color: #FFFFFF;
    margin: 0 auto;
    position: relative;

    .logo {
      display: flex;
      justify-content: space-between;
      .footer-logo-wrap {
        .footer-logo {
          width: 97px;

          figure , img {
            width: 100%;
          }
        }

        .email{
          height: 14px;
          font-size: 12px;
          line-height: 14px;
          margin-top: 8px;
        }
      }
      .footer-logo-cert {
        img {
          width: 38px;
          height: 38px;
        }
      }


    }

    .slogon {
      display: flex;
      font-size: 12px;
      margin-top: 20px;
      justify-content: space-between;

      a, a:active {
        color: #fff;
      }
    }

    .social {
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      .text {
        font-size: 10px;
        font-weight: 300;
        color: #99A6BF;
        line-height: 24px;
      }

      .imgs {
        display: flex;
        justify-content: space-between;
        width: 108px;

        figure {
          cursor: pointer;
          img {
            width: 24px;
            height: 24px;
          }
        }
      }
    }

    .spliter {
      height: 1px;
      background-color: #424752;
      // transform: scaleY(0.5);
      margin: 11px 0;
      position: relative;
    }

    .compliance {
      display: flex;

      .content-zh{
        span,a{
          margin-right: 8px;
        }
      }
      .content-en,.content-zh{
        font-size: 10px;
        line-height: 12px;
        display: flex;
        flex-wrap: wrap;

        span,
        a {
          // margin-right: 16px;
          text-decoration: none;
          color: #99A6BF;
          transition: all .2s;
        }

        a {
          &:hover {
            color: #FF8600;
            transition: all .2s;
          }
        }
      }

      .language-selector{
        margin-left: 20px;
        .text{
          white-space:nowrap;
          color: #fff;
          font-size: 12px;
          padding: 0px 8px;
          height: 23px;
          border-radius: 2px;
          border: 1px solid #FFFFFF;
          text-align: center;
          line-height: 23px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}

.overlay {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 100;
  top: 0;
  left: 0;
  background: rgba(0,0,0,0.4);
  display: flex;
  align-items: center;

  .dialog {
    width: 100%;
    // height: calc(100% - 60px);
    // position: fixed;
    top: 0;
    left: 0;
    top: 30px;
    box-sizing: border-box;
    padding: 0 15px;
    
    &-box {
      width: 100%;
      height: 100%;
      background: #FFFFFF;
      box-sizing: border-box;
      padding: 36px 0;
      position: relative;
      border-radius: 4px;
      overflow-y: auto;
      overflow-x: hidden;
    }

    .title {
      text-align: center;
      width: 250px;
      font-size: 24px;
      font-weight: 500;
      color: #484848;
      line-height: 24px;
      margin: 0 auto;
    }

    .dialog-close {
      padding: 16px;
      width: 14px;
      height: 14px;
      position: absolute;
      left: 0;
      top: 0;

      img {
        cursor: pointer;
      }
    }

    .content {
      // display: flex;
      margin-top: 32px;
      padding: 0 74px;

      .col {
        text-align: center;
        margin-top: 30px;

        &-title {
          font-size: 14px;
          font-weight: bold;
          color: #454545;
          line-height: 19px;
        }

        .img-box {
          // width: 170px;
          padding: 24px 35px 16px 35px;
          border-radius: 8px;
          margin: 0 auto;
          box-sizing: border-box;
          margin-top: 8px;
          background: #F7F8FA;

          img {
            width: 100%;
          }

          .wechat-qr {
            margin: 0 auto 8px auto;
            width: 92px;
            height: 92px;
            background: #FFFFFF;
            border-radius: 8px;
            border: 1px solid #454545;
            padding: 3px;
          }

          .guide-pc {
            width: 100px;
            height: 100px;
            margin: -13px auto 0px auto;
          }

          .text {
            font-size: 12px;
            color: rgba(0,0,0,0.85);
            line-height: 14px;
          }

          .link {
            font-size: 12px;
            font-weight: 300;
            color: #FF8600;
            line-height: 14px;
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>
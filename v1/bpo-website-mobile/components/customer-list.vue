<template lang="pug">
el-carousel(
  ref="carouselRef" 
  indicator-position="outside" 
  :autoplay="true" 
  height='160px' 
  :interval="2500" 
  :initial-index="0")
  //- 第一页
  el-carousel-item(v-for="(item, itemIndex) in customerList")
    .customer-list(:key="itemIndex")
      .customer-item(v-for="(row, rowIndex) in item")
        figure(:key="rowIndex")
          img(:src="getImageUrl(row)")
          //- img(src="~/assets/images/customer/1-1.png")

</template>
  
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ElCarousel, ElCarouselItem } from 'element-plus'

const carouselRef = ref()

const customerList = ref([])

const modules: any = import.meta.glob('~/assets/images/customer/*', { eager: true }) // 这里可以写 @ (路径别名) 或者 /src (绝对或相对路径)

const getImageUrl = (name: string) => {
  const src = `/assets/images/customer/${name}`

  return modules[src] && modules[src].default
}

const slideBanner = () => {
  //选中item的盒子
  let box = document.querySelector('.el-carousel__container');
  
  //手指起点X坐标
  let startPoint = 0;
  //手指滑动重点X坐标
  let stopPoint = 0;

  //重置坐标
  let resetPoint =  function(){
    startPoint = 0;
    stopPoint = 0;
  }
  
  //手指按下
  box.addEventListener("touchstart",function(e: any){
    //手指按下的时候停止自动轮播
    // vm.stopAuto();
    //手指点击位置的X坐标
    startPoint = e.changedTouches[0].pageX;
  });
  
  //手指滑动
  box.addEventListener("touchmove",function(e: any){
    //手指滑动后终点位置X的坐标
    stopPoint = e.changedTouches[0].pageX;
  });

  //当手指抬起的时候，判断图片滚动离左右的距离
  box.addEventListener("touchend",function(e){

    // console.log("1："+startPoint);
    // console.log("2："+stopPoint);
  
    if(stopPoint == 0 || startPoint - stopPoint == 0){
    resetPoint();
      return;
    }

    if(startPoint - stopPoint > 0){
      resetPoint();
      carouselRef.value.next();
      return;
    }

    if(startPoint - stopPoint < 0){
      resetPoint();
      carouselRef.value.prev();
      return;
    }
  });
}

onMounted(() => {
  slideBanner()
  
  const list = [
    "249.png","250.png","251.png",
    "220.png","221.png","222.png","223.png","224.png","225.png","226.png",
    "227.png","228.png","229.png","230.png","231.png","232.png","233.png",
    "234.png","235.png","236.png","237.png","238.png","239.png","240.png",
    "241.png","242.png","243.png","244.png","245.png","246.png","247.png","248.png",
    "175.png","176.png","177.png","178.png","179.png","180.png","181.png",
    "182.png","183.png","184.png","185.png","186.png","187.png","188.png",
    "189.png","190.png","191.png","192.png","193.png","194.png","195.png",
    "196.png","197.png","198.png","199.png","200.png","201.png","202.png",
    "203.png","204.png","205.png","206.png","207.png","208.png","209.png",
    "210.png","211.png","212.png","213.png","214.png","215.png","216.png",
    "217.png","218.png","219.png","1-1.png","1-2.png","1-3.png","1-4.png",
    "1-5.png","1-6.png","1-7.png","1-8.png","1-9.png","2.png","3.png","4.png",
    "7.png","8.png","9.png","11.png","14.png","15.png","16.png","17.png","18.png",
    "19.png","20.png","22.png","23.png","24.png","27.png","28.png","29.png","31.png",
    "34.png","35.png","36.png","37.png","38.png","39.png","41.png","43.png","44.png",
    "45.png","46.png","48.png","49.png","50.png","51.png","52.png","54.png","55.png",
    "56.png","60.png","61.png","62.png","64.png","65.png","66.png","67.png","69.png",
    "70.png","71.png","72.png","74.png","75.png","76.png","77.png","79.png","81.png",
    "83.png","84.png","86.png","88.png","89.png","91.png","92.png","93.png","95.png",
    "96.png","97.png","98.png","100.png","101.png","102.png","103.png","105.png","107.png",
    "109.png","110.png","112.png","114.png","115.png","116.png","117.png","120.png","122.png",
    "123.png","125.png","126.png","129.png","130.png","131.png","133.png","135.png","136.png",
    "137.png","138.png","139.png","140.png","142.png","143.png","144.png","145.png","146.png",
    "148.png","149.png","150.png","151.png","152.png","154.png","155.png","156.png","157.png",
    "160.png","161.png","162.png","163.png","164.png","165.png","166.png","167.png","168.png",
    "169.png","170.png","171.png","172.png","173.png","174.png"
  ]
  
  const result = []; 
  for (let i = 0; i < list.length; i += 16) {  
    result.push(list.slice(i, i + 16));  
  }

  customerList.value = result
})
</script>
  
<style lang="scss" scoped>

  :deep(.el-carousel__arrow) {
    display: none !important;
  }

  :deep(.el-carousel__indicators) {
    pointer-events: none;

    .el-carousel__button {
      width: 15px;
      pointer-events: none;
    }
  }

  .customer-list {
    display: flex;
    flex-wrap: wrap;

    .customer-item {
      width: 25%;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 75px;
        height: 28px;
      }
    }
  }
</style>
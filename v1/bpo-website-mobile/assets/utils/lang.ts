/*
 * @Author: sx <EMAIL>
 * @Date: 2022-12-14 16:02:20
 * @LastEditors: sx <EMAIL>
 * @LastEditTime: 2023-01-13 13:02:16
 * @FilePath: \bpo-website-mobile\assets\utils\lang.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

// 注意：这些函数是为 Nuxt.js 设计的，在 Next.js 环境中已被替代
const LANG_COOKIE_KEY = 'sd_intl_lang'

type Lang = 'en' | 'zh'

// 首页引导 - 已废弃，改为 Next.js 路由
export function indexGuide() {
  console.warn('indexGuide is deprecated in Next.js version');
  // 在 Next.js 版本中，这个逻辑已经被路由系统替代
}

// 切换语言 - 已废弃，改为 Next.js 路由
export function swithLang(lang: Lang, path: string = '') {
  console.warn('swithLang is deprecated in Next.js version, use switchLanguage from src/utils/lang.ts instead');
  // 在 Next.js 版本中，使用 src/utils/lang.ts 中的 switchLanguage 函数
}

const langUtils = {
  indexGuide,
  swithLang
};

export default langUtils;
/*
 * @Author: xu.sun <EMAIL>
 * @Date: 2022-11-18 17:08:24
 * @LastEditors: xu.sun <EMAIL>
 * @LastEditTime: 2022-11-18 18:22:25
 * @FilePath: /bpo-website-pc/assets/utils/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

// 注意：这些函数是为 Nuxt.js 设计的，在 Next.js 环境中已被替代
// 平台切换 - 已废弃，改为响应式设计
export function switchingPlatforms() {
  // 这个函数在 Next.js 版本中已被移除
  // 原来的移动端跳转逻辑已改为响应式设计
  console.warn('switchingPlatforms is deprecated in Next.js version');
}

// 获取url 参数
export function getQueryString(name: string) {
  if (typeof window === 'undefined') return null;
  
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
  const r = window.location.search.substring(1).match(reg);
  if (r != null) {
    return unescape(r[2]);
  }
  return null;
}
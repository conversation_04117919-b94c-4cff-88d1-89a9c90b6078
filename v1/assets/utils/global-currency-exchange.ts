const CURRENCY_EXCHANGE_RATE = {
  "USD": 1,
  "CNY": 7.2385,
  "HKD": 7.8142,
  "AED": 3.6729
} as const;

type CurrencyCode = keyof typeof CURRENCY_EXCHANGE_RATE;

export function exchangeConvert(from: Cur<PERSON>cy<PERSON><PERSON>, to: Currency<PERSON>ode, amount: number) {
  if (from === to) {
    return amount
  }
  const usdAmount = amount / CURRENCY_EXCHANGE_RATE[from]
  if (to === "USD") {
    return usdAmount
  }
  return usdAmount * CURRENCY_EXCHANGE_RATE[to]
}
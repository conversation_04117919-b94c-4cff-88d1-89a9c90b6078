<!-- Generated by SpecStory -->

# Vue Component Logic Refactoring (2025-05-22 10:04Z)

_**User**_

参考 @calculator.vue 把 @calculator.vue 代码逻辑改成一样的，谢谢

---

_**Assistant**_

你要把 `pages/zh/calculator.vue` 的代码逻辑改成和 `pages/en/calculator.vue` 一样，意思是：

- 不要用接口动态拉取国家、省份、币种、员工类型，直接用静态的 reactive 配置（supportCountries、supportProvinces、supportCurrencies、supportEmployType）
- 不要用 `fetchEmployCost` 这种接口，直接用本地的 `costConfig` 计算
- 表单字段、表单校验、切换国家时的逻辑、结果展示等都和英文版保持一致
- 只保留 `calculateResult`，不要用 `calculateRes`
- 代码风格、变量名、结构都和英文版一致
- 你可以保留中文文案和样式

下面是你要做的主要改动：

1. 删除所有 fetch 相关的逻辑（fetchCountry、fetchCurrency、fetchEmployCost、onCountryChange 相关的接口调用等）
2. 把国家、省份、币种、员工类型的列表都用 reactive 静态对象，和英文版一样
3. 表单字段、表单校验、切换国家时的逻辑、结果展示等都和英文版保持一致
4. 结果展示部分用 calculateResult，字段和英文版一致
5. 只保留本地计算逻辑 calculateCost，和英文版一致
6. 组件 import 方式和英文版一致（不要用解构，直接 import { ElForm, ... } from 'element-plus'）

下面直接给你主要代码片段（只列出关键部分，别的地方直接照抄英文版，文案用中文）：

---

**1. 静态配置：**
```ts
const supportCountries = reactive([
  { value: "CN", label: "中国大陆" },
  { value: "HK", label: "中国香港" },
  { value: "AE", label: "阿联酋" }
])

const supportProvinces = reactive({
  "CN": [
    { value: "BJ", label: "北京" },
    { value: "SH", label: "上海" }
  ],
  "HK": [],
  "AE": []
})

const supportCurrencies = reactive({
  "CN": [{ value: "CNY", label: "CNY" }],
  "HK": [{ value: "HKD", label: "HKD" }],
  "AE": [{ value: "AED", label: "AED" }]
})

const supportEmployType = reactive({
  "AE": [
    { value: "LOCAL", label: "本地" },
    { value: "EXPAT", label: "外籍" }
  ]
})
```

---

**2. 表单和结果：**
```ts
const form = reactive({
  country: '',
  province: '',
  currency: '',
  employType: '',
  annualSalary: null
})

const calculateResult = reactive({
  annualSalary: 0,
  currency: null,
  totalEmployerCost: null,
  costList: [],
  totalAnnualCost: null
})
```

---

**3. 切换国家时的逻辑：**
```ts
function handleCountryChange() {
  form.employType = ''
  form.province = ''
  form.currency = supportCurrencies[form.country][0].value
}
```

---

**4. 计算逻辑（直接用 costConfig）：**
```ts
async function calculateCost() {
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      let areaKey = form.country
      if (form.province) {
        areaKey += '-' + form.province
      }
      if (form.employType) {
        areaKey += '-' + form.employType
      }

      calculateResult.annualSalary = form.annualSalary
      calculateResult.totalEmployerCost = 0
      calculateResult.costList = []
      calculateResult.totalAnnualCost = 0

      const monthlySalary = form.annualSalary / 12
      for (let i = 0; i < costConfig[areaKey].sections.length; i++) {
        const costItem = costConfig[areaKey].sections[i]
        const useSalary = costItem.salaryType === 'month' ? monthlySalary : form.annualSalary
        for (let j = 0; j < costItem.steps.length; j++) {
          let actualCostItem = {
            costName: costItem.sectionName,
            currency: costItem.currency,
            costAmount: null
          }
          if (useSalary >= costItem.steps[j].salaryFrom && (costItem.steps[j].salaryTo === -1 || useSalary < costItem.steps[j].salaryTo)) {
            let actualCostAmount = 0
            if (costItem.steps[j].fixAmount > 0) {
              actualCostAmount = costItem.steps[j].fixAmount
            } else {
              actualCostAmount = useSalary * costItem.steps[j].costRate / 100
            }
            if (actualCostAmount > costItem.costLimit.max) {
              actualCostAmount = costItem.costLimit.max
            }
            if (actualCostAmount < costItem.costLimit.min) {
              actualCostAmount = costItem.costLimit.min
            }
            if (costItem.salaryType === 'month') {
              actualCostAmount *= 12
            }
            actualCostItem.costAmount = actualCostAmount.toFixed(2)
            calculateResult.currency = costItem.currency
            calculateResult.totalEmployerCost = calculateResult.totalEmployerCost + actualCostAmount
            calculateResult.totalAnnualCost = calculateResult.totalEmployerCost + actualCostAmount + Number(form.annualSalary)
            calculateResult.costList.push(actualCostItem)
            break
          }
        }
      }
      status.showTable = true
      scrollTo("#calculate-result")
    }
  })
}
```

---

**5. 结果展示部分用 calculateResult，字段和英文版一致。**

---

**6. 组件 import 方式和英文版一致：**
```ts
import { ElDialog, ElForm, ElFormItem, ElButton, ElInput, ElSelect, ElOption, ElMessage } from 'element-plus'
```

---

**7. 删除所有 fetch、接口、动态列表相关的代码。**

---

**8. 其它结构、样式、文案可以保留中文。**

---

你只要把 `pages/zh/calculator.vue` 按照上面这些点全量替换/重构，和英文版保持一致，所有逻辑都本地化，表单和结果展示字段都和英文版一样，变量名也一样。  
这样你就不会有类型报错、接口依赖、字段不一致等问题了。

如果你要完整代码，告诉我。

---

_**User**_

我需要的是英文版本的改为和中文版的业务逻辑一样，请直接帮我写代码，谢谢

---

_**Assistant**_

<details><summary>Edit file: pages/en/calculator.vue</summary>

把英文版的 calculator.vue 改成和中文版一样的业务逻辑：1. 用接口动态拉取国家、省份、币种、员工类型（fetchCountry、fetchCurrency），2. 用 fetchEmployCost 接口获取计算结果，3. 表单字段、切换国家、结果展示等都和中文版一致，4. 变量名、结构、字段都和中文版一致，5. 结果展示用 calculateRes，6. 文案保持英文。

**Chunk 1**
Lines added: 21, lines removed: 23

```diff
@@ -13,41 +13,39 @@
      .header-banner
        .header-banner-text
-         h1.header-title Employee costs across the globe
-         p.header-desc The location of the base company's staff is immediately available for invitation, and is the most suitable invitation method for the establishment of a permanent company.
+         h1.header-title Determine employee costs across the globe
+         p.header-desc Ready to hire in another country? Instantly aggregate employment costs based on team member locations to make the best hiring move for your business.
        .header-form
          h2.calculator-title Employee Cost Calculator
-         p.calculator-desc Select the country/region you wish to invite, and explore the standards for establishing international positions.
+         p.calculator-desc Select the country/region you wish to hire, and enter the employee's annual salary to check the employer cost in that location.
          ClientOnly
            .form-body
-             el-form(:model="form" ref="formRef")
-               el-form-item(:rules="[{required: true, message:'Please select the country'}]" prop="country")
-                 el-select(size="large" placeholder="Country" v-model="form.country" @change="handleCountryChange" style="width:100%;")
-                   el-option(v-for="(country, country_index) in supportCountries" :key="country_index" :label="country.label" :value="country.value")
+             el-form(:model="form" ref="formRef" @change="handleFormChange")
+               el-form-item(:rules="[{required: true, message:'Please select the country/region'}]" prop="country")
+                 el-select(size="large" placeholder="Country/Region" v-model="form.country" @change="onCountryChange" style="width:100%;")
+                   el-option(v-for="item in countryList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode")
  
-               el-form-item(:rules="[{required: true, message:'Please select the province'}]" v-if="form.country === 'CN'" prop="province")
-                 el-select(size="large" placeholder="State" v-model="form.province" prop="province" style="width:100%;")
-                   el-option(v-for="(province, province_index) in supportProvinces[form.country]" :key="province_index" :label="province.label" :value="province.value")
+               el-form-item(:rules="[{required: true, message:'Please select the province/city'}]" v-if="provinceList.length" prop="province")
+                 el-select(size="large" placeholder="Province/City" v-model="form.province" @change="handleFormChange" style="width:100%;")
+                   el-option(v-for="item in provinceList" :key="item.key" :label="item.name" :value="item.key")
  
-               el-form-item(:rules="[{required: true, message:'Please select the currency'}]" prop="currency")
-                 el-select(size="large" placeholder="Currency" v-model="form.currency" style="width:100%;")
-                   el-option(v-for="(currency, currency_index) in supportCurrencies[form.country]" :key="currency_index" :label="currency.label" :value="currency.value")
+               el-form-item(:rules="[{required: true, message:'Please select employee type'}]" v-if="employTypeList.length" prop="employType")
+                 el-select(size="large" placeholder="Employee Type" v-model="form.employType" @change="handleFormChange" style="width:100%;")
+                   el-option(v-for="item in employTypeList" :key="item.key" :label="item.employTypeName" :value="item.key")
  
-               el-form-item(:rules="[{required: true, message:'请选择员工类型'}]" v-if="form.country === 'AE'" prop="employType")
-                 el-select(size="large" placeholder="员工类型" v-model="form.employType" style="width:100%;")
-                   el-option(v-for="(employType, employ_type_index) in supportEmployType[form.country]" :key="employ_type_index" :label="employType.label" :value="employType.value")
+               el-form-item(v-if="form.currency" :rules="[{required: true, message:'Please select the currency'}]" prop="currency")
+                 el-input(size="large" v-model="form.currency" disabled style="width:100%;")
  
-               el-form-item(:rules="[{required:true, message:'Please input the annual salary'}]" prop="annualSalary")
-                 el-input(placeholder="Gross annual salary" size="large" v-model.number="form.annualSalary")
- 
-               el-form-item()
-                 el-button.calculator-submit(@click="calculateCost") Calculate
+               el-form-item(:rules="[{required: true, message:'Please input the annual salary'}]" prop="annualSalary")
+                 el-input(size="large" v-model.number="form.annualSalary" placeholder="Gross annual salary" @input="handleFormChange" style="width:100%;")
+ 
+               el-button.calculator-submit(@click="fetchEmployCost") Get Employer Cost
    .calculator-container(id="calculate-result")
      h2.result-title Determine employee costs across the globe
-     p.result-description Ready to hire in another country? Instantly aggregate employment costs based on team member locations to make the best hiring move for your business.
+     p.result-description SmartDeer can help you decide the best place to hire your next employee and get a comprehensive understanding of the taxes, fees, benefits, and more you can expect to pay.
      ClientOnly
        table.result-table(v-if="status.showTable")
          tr
            td.cost-name Gross Annual Salary
-           td.cost-amount {{ calculateResult.currency }} {{ formatNumber(calculateResult.annualSalary) }}
+           td.cost-amount {{ form?.currency }} {{ formatNumber(calculateRes?.salary) }}
          tr
            td.cost-name.cost-position
```

**Chunk 2**
Lines added: 6, lines removed: 6

```diff
@@ -56,14 +54,14 @@
                ArrowUp(v-if="status.showTableDetails" @click="() => {status.showTableDetails = false}")
              span Total Annual Employer Costs
-           td.cost-amount {{ calculateResult.currency }} {{ formatNumber(calculateResult.totalEmployerCost) }}
-         tr(v-for="(cost, cost_index) in calculateResult.costList" v-if="status.showTableDetails")
-           td.cost-name-detail {{ cost.costName }}
-           td.cost-amount-detail {{ cost.currency }} {{ formatNumber(cost.costAmount) }}
+           td.cost-amount {{ form?.currency }} {{ formatNumber(calculateRes.netTotalCosts) }}
+         tr(v-for="(cost, cost_index) in calculateRes?.costs" v-if="status.showTableDetails")
+           td.cost-name-detail {{ cost?.sectionName }}
+           td.cost-amount-detail {{ form?.currency }} {{ formatNumber(cost?.costOrigin) }}
          tr
            td.cost-name Total annual cost
-           td.cost-amount {{ calculateResult.currency }} {{ formatNumber(calculateResult.totalAnnualCost) }}
+           td.cost-amount {{ form?.currency }} {{ formatNumber(calculateRes?.totalCosts) }}
        p.result-notice(v-if="status.showTable") Calculations are estimates based on a country's local tax and compliance costs, and net payments and employer contributions may vary based on individual employee data.
        p.entity-notice SmartDeer has entities worldwide. This means you can hire employees in all of these countries without opening your own corporate entity.
-     button.calculator-contact(@click="handleContactUs") 联系我们
+     button.calculator-contact(@click="handleContactUs") Contact Us
    +contact-us
    site-footer(lang="zh" @contact-us="()=>{status.showForm=true}")
```

**Chunk 3**
Lines added: 1, lines removed: 1

```diff
@@ -74,5 +72,5 @@
  import langTool from '~/assets/utils/lang'
  import {getQueryString} from "assets/utils";
- import {reactive} from "@vue/reactivity";
+ import {reactive, ref, onMounted, onBeforeUnmount} from "@vue/reactivity";
  import costConfig from '~/assets/utils/global-cost-config';
  import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
```

**Chunk 4**
Lines added: 3, lines removed: 57

```diff
@@ -93,61 +91,7 @@
  const formRef = ref()
  
- const supportCountries = reactive([
-   {
-     "value": "CN",
-     "label": "China Mainland"
-   },
-   {
-     "value": "HK",
-     "label": "Hong Kong"
-   },
-   {
-     "value": "AE",
-     "label": "United Arab Emirates"
-   }
- ])
- 
- const supportProvinces = reactive({
-   "CN": [
-     {
-       "value": "BJ",
-       "label": "Beijing"
-     },
-     {
-       "value": "SH",
-       "label": "Shanghai"
-     }
-   ],
-   "HK": [],
-   "AE": []
- })
- 
- const supportCurrencies = reactive({
-   "CN": [{
-     "value": "CNY",
-     "label": "CNY"
-   }],
-   "HK": [{
-     "value": "HKD",
-     "label": "HKD"
-   }],
-   "AE": [{
-     "value": "AED",
-     "label": "AED"
-   }]
- })
- 
- const supportEmployType = reactive({
-   "AE": [
-     {
-       "value": "LOCAL",
-       "label": "Local"
-     },
-     {
-       "value": "EXPAT",
-       "label": "Expat"
-     }
-   ]
- })
+ const countryList = ref([])
+ const provinceList = ref([])
+ const employTypeList = ref([])
  
  const form = reactive({
```

**Chunk 5**
Lines added: 3, lines removed: 12

```diff
@@ -159,21 +103,12 @@
  })
  
- const calculateResult = reactive({
-   annualSalary: 0,
-   currency: null,
-   totalEmployerCost: null,
-   costList: [],
-   totalAnnualCost: null
- })
+ const calculateRes = ref({})
  
- function handleCountryChange() {
-   form.employType = ''
-   form.province = ''
-   form.currency = supportCurrencies[form.country][0].value
+ function handleFormChange() {
+   // fetchEmployCost();
  }
  
  function handleContactUs() {
    status.showForm = true
-   console.log(status)
  }
  
```

**Chunk 6**
Lines added: 98, lines removed: 58

```diff
@@ -186,62 +121,102 @@
  }
  
- async function calculateCost() {
-   await formRef.value.validate((valid, fields) => {
-     if (valid) {
-       let areaKey = form.country
-       if (form.province) {
-         areaKey += '-' + form.province
-       }
-       if (form.employType) {
-         areaKey += '-' + form.employType
-       }
- 
-       calculateResult.annualSalary = form.annualSalary
-       calculateResult.totalEmployerCost = 0
-       calculateResult.costList = []
-       calculateResult.totalAnnualCost = 0
- 
- 
-       const monthlySalary = form.annualSalary / 12
-       for (let i = 0; i < costConfig[areaKey].sections.length; i++) {
-         const costItem = costConfig[areaKey].sections[i]
-         const useSalary = costItem.salaryType === 'month' ? monthlySalary : form.annualSalary
-         for (let j = 0; j < costItem.steps.length; j++) {
-           let actualCostItem = {
-             costName: costItem.sectionNameEn,
-             currency: costItem.currency,
-             costAmount: null
-           }
-           if (useSalary >= costItem.steps[j].salaryFrom && (costItem.steps[j].salaryTo === -1 || useSalary < costItem.steps[j].salaryTo)) {
-             let actualCostAmount = 0
-             if (costItem.steps[j].fixAmount > 0) {
-               actualCostAmount = costItem.steps[j].fixAmount
-             } else {
-               actualCostAmount = useSalary * costItem.steps[j].costRate / 100
-             }
-             if (actualCostAmount > costItem.costLimit.max) {
-               actualCostAmount = costItem.costLimit.max
-             }
-             if (actualCostAmount < costItem.costLimit.min) {
-               actualCostAmount = costItem.costLimit.min
-             }
-             if (costItem.salaryType === 'month') {
-               actualCostAmount *= 12
-             }
-             actualCostItem.costAmount = actualCostAmount.toFixed(2)
-             calculateResult.currency = costItem.currency
-             calculateResult.totalEmployerCost = calculateResult.totalEmployerCost + actualCostAmount
-             calculateResult.totalAnnualCost = calculateResult.totalEmployerCost + actualCostAmount + Number(form.annualSalary)
-             calculateResult.costList.push(actualCostItem)
-             break
-           }
-         }
-       }
-       status.showTable = true
-       scrollTo("#calculate-result")
-       console.log(calculateResult)
-     }
-   })
- }
+ const host = "https://www-api-test.smartdeer.work/v1/website/function/runtime";
+ const fetchCountry = async () => {
+   const res = await fetch(host, {
+     method: "POST",
+     headers: {
+       "Content-Type": "application/json",
+     },
+     body: JSON.stringify({
+       functionKey: "search_website_smd_employer_cost_country_conf",
+       params: {
+         current: 1,
+         size: 100,
+         limit: 100,
+         searchInfo: {
+           searchItems: [],
+           orders: [
+             {
+               key: "displayOrder",
+               asc: "ASC",
+             },
+           ],
+         },
+       },
+     }),
+   });
+   const {
+     data: { dataInfo },
+   } = await res.json();
+   countryList.value = dataInfo || [];
+ };
+ 
+ const fetchCurrency = async (countryCode) => {
+   const res = await fetch(`${host}`, {
+     method: "POST",
+     headers: {
+       "Content-Type": "application/json",
+     },
+     body: JSON.stringify({
+       functionKey: "x_website_employer_cost_country_conf",
+       params: {
+         countryCode,
+       },
+     }),
+   });
+   const { data } = await res.json();
+   provinceList.value = data?.provinceConf || [];
+   employTypeList.value = data?.employTypeConf || [];
+   form.currency = data?.currencyConf || "";
+ };
+ 
+ const fetchEmployCost = async () => {
+   if (!form.country || !form.annualSalary) return;
+   const params: any = {
+     countryCode: form.country,
+     employeeSalary: form.annualSalary,
+     ...(provinceList.value.length && form.province
+       ? { provinceCode: form.province }
+       : {}),
+     ...(employTypeList.value.length && form.employType
+       ? { employType: form.employType }
+       : {}),
+   };
+   const res = await fetch(host, {
+     method: "POST",
+     headers: { "Content-Type": "application/json" },
+     body: JSON.stringify({
+       functionKey: "x_website_cal_employer_cost",
+       params,
+     }),
+   });
+   const { data } = await res.json();
+   calculateRes.value = data;
+   scrollTo("#calculate-result");
+ };
+ 
+ const onCountryChange = async (val) => {
+   form.province = "";
+   form.employType = "";
+   form.currency = "";
+   provinceList.value = [];
+   employTypeList.value = [];
+   await fetchCurrency(val);
+   handleFormChange();
+ };
+ 
+ onMounted(() => {
+   window.addEventListener('scroll', scroll)
+ 
+   const curScroll = getQueryString('scroll')
+   if (curScroll) {
+     scrollTo('#' + curScroll)
+   }
+   fetchCountry();
+ })
+ 
+ onBeforeUnmount(() => {
+   window.removeEventListener('scroll', scroll)
+ })
  
  function scrollTo(tag) {
```

**Chunk 7**
Lines added: undefined, lines removed: 13

```diff
@@ -275,17 +250,4 @@
    }, 30)
  }
- 
- onMounted(() => {
-   window.addEventListener('scroll', scroll)
- 
-   const curScroll = getQueryString('scroll')
-   if (curScroll) {
-     scrollTo('#' + curScroll)
-   }
- })
- 
- onBeforeUnmount(() => {
-   window.removeEventListener('scroll', scroll)
- })
  
  function switchLang(lang) {
```

</details>

---

<details><summary>Edit file: pages/en/calculator.vue</summary>

修正 import，onMounted 和 onBeforeUnmount 应该从 'vue' 导入，不是 '@vue/reactivity'。

**Chunk 1**
Lines added: 1, lines removed: 1

```diff
@@ -72,5 +72,5 @@
  import langTool from '~/assets/utils/lang'
  import {getQueryString} from "assets/utils";
- import {reactive, ref, onMounted, onBeforeUnmount} from "@vue/reactivity";
+ import { reactive, ref, onMounted, onBeforeUnmount } from "vue";
  import costConfig from '~/assets/utils/global-cost-config';
  import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
```

</details>

---

已经把英文版 calculator.vue 改成和中文版一样的业务逻辑了，主要点如下：

- 国家、省份、币种、员工类型都通过接口动态拉取
- 计算结果通过 fetchEmployCost 接口获取
- 表单、切换国家、结果展示等都和中文版一致
- 变量名、结构、字段都和中文版一致，结果展示用 calculateRes
- 文案保持英文
- 修正了 onMounted/onBeforeUnmount 的 import（从 vue 导入）

你现在只需要根据实际接口返回的字段调整下字段名（如果有出入），其它都和中文版一致。

如果还有别的需求或者要补充的细节，直接说。

---

_**User**_

template pug 都改成 与 中文版的html结构一样，

---

_**Assistant**_

<details><summary>Edit file: pages/en/calculator.vue</summary>

把 template 部分的 pug 结构全部改成和中文版 calculator.vue 一样的标准 HTML 结构，内容、class、结构、表单、table、按钮等全部一一对应，只保留英文文案。不要用 pug，直接用标准 HTML。

**Chunk 1**
Lines added: 184, lines removed: 66

```diff
@@ -1,69 +1,187 @@
- <template lang="pug">
- mixin contact-us
-   .contact-form
-     client-only
-       el-dialog(v-model="status.showForm" title="" :width="600")
-         contact-us-form(@submit="submitSuccess" lang="en")
- .contries-page
-   header
-     site-header(
+ <template>
+   <div class="contries-page">
+     <header>
+       <site-header lang="en" source="countries" :showContactUs="false" />
+       <div class="header-banner">
+         <div class="header-banner-text">
+           <h1 class="header-title">Determine employee costs across the globe</h1>
+           <p class="header-desc">
+             Ready to hire in another country? Instantly aggregate employment costs based on team member locations to make the best hiring move for your business.
+           </p>
+         </div>
+         <div class="header-form">
+           <h2 class="calculator-title">Employee Cost Calculator</h2>
+           <p class="calculator-desc">
+             Select the country/region you wish to hire, and enter the employee's annual salary to check the employer cost in that location.
+           </p>
+           <div class="form-body">
+             <el-form
+               :model="form"
+               ref="formRef"
+               @change="handleFormChange"
+             >
+               <el-form-item
+                 label=""
+                 prop="country"
+                 :rules="[
+                   { required: true, message: 'Please select the country/region' },
+                 ]"
+                 ><el-select
+                   size="large"
+                   v-model="form.country"
+                   placeholder="Country/Region"
+                   @change="onCountryChange"
+                   style="width: 100%"
+                 >
+                   <el-option
+                     v-for="item in countryList"
+                     :key="item.areaCode"
+                     :label="item.areaName"
+                     :value="item.areaCode"
+                   />
+                 </el-select>
+               </el-form-item>
+               <el-form-item
+                 v-if="provinceList.length"
+                 label=""
+                 prop="province"
+                 :rules="[
+                   { required: true, message: 'Please select the province/city' },
+                 ]"
+                 ><el-select
+                   size="large"
+                   v-model="form.province"
+                   placeholder="Province/City"
+                   @change="handleFormChange"
+                   style="width: 100%"
+                 >
+                   <el-option
+                     v-for="item in provinceList"
+                     :key="item.key"
+                     :label="item.name"
+                     :value="item.key"
+                   />
+                 </el-select>
+               </el-form-item>
+               <el-form-item
+                 v-if="employTypeList.length"
+                 label=""
+                 prop="employType"
+                 :rules="[{ required: true, message: 'Please select employee type' }]"
+                 ><el-select
+                   size="large"
+                   v-model="form.employType"
+                   placeholder="Employee Type"
+                   @change="handleFormChange"
+                   style="width: 100%"
+                 >
+                   <el-option
+                     v-for="item in employTypeList"
+                     :key="item.key"
+                     :label="item.employTypeName"
+                     :value="item.key"
+                   />
+                 </el-select>
+               </el-form-item>
+               <el-form-item v-if="form.currency" label="" prop="currency">
+                 <el-input
+                   size="large"
+                   v-model="form.currency"
+                   disabled
+                   style="width: 100%"
+                 />
+               </el-form-item>
+               <el-form-item
+                 label=""
+                 prop="annualSalary"
+                 :rules="[
+                   { required: true, message: 'Please input the annual salary' },
+                   { type: 'number', message: 'Please enter a valid number.' },
+                 ]"
+                 ><el-input
+                   size="large"
+                   v-model.number="form.annualSalary"
+                   placeholder="Gross annual salary"
+                   @input="handleFormChange"
+                   style="width: 100%"
+                 />
+               </el-form-item>
+               <el-button class="calculator-submit" @click="fetchEmployCost"
+                 >Get Employer Cost</el-button
+               >
+             </el-form>
+           </div>
+         </div>
+       </div>
+     </header>
+     <div id="calculate-result" class="calculator-container">
+       <h2 class="result-title">Determine employee costs across the globe</h2>
+       <p class="result-description">
+         SmartDeer can help you decide the best place to hire your next employee and get a comprehensive understanding of the taxes, fees, benefits, and more you can expect to pay.
+       </p>
+       <table v-if="status.showTable" class="result-table">
+         <tr>
+           <td class="cost-name">Gross Annual Salary</td>
+           <td class="cost-amount">
+             {{ form?.currency }}
+             {{ formatNumber(calculateRes?.salary) }}
+           </td>
+         </tr>
+         <tr>
+           <td class="cost-name cost-position">
+             <span class="icon">
+               <ArrowDown
+                 v-if="!status.showTableDetails"
+                 @click="() => { status.showTableDetails = true; }"
+               />
+               <ArrowUp
+                 v-if="status.showTableDetails"
+                 @click="() => { status.showTableDetails = false; }"
+               />
+             </span>
+             <span>Total Annual Employer Costs</span>
+           </td>
+           <td class="cost-amount">
+             {{ form?.currency }}
+             {{ formatNumber(calculateRes.netTotalCosts) }}
+           </td>
+         </tr>
+         <template v-if="status.showTableDetails">
+           <tr
+             v-for="(cost, cost_index) in calculateRes?.costs"
+             :key="cost_index"
+           >
+             <td class="cost-name-detail">{{ cost?.sectionName }}</td>
+             <td class="cost-amount-detail">
+               {{ form?.currency }} {{ formatNumber(cost?.costOrigin) }}
+             </td>
+           </tr>
+         </template>
+         <tr>
+           <td class="cost-name">Total annual cost</td>
+           <td class="cost-amount">
+             {{ form?.currency }}
+             {{ formatNumber(calculateRes?.totalCosts) }}
+           </td>
+         </tr>
+       </table>
+       <p v-if="status.showTable" class="result-notice">
+         Calculations are estimates based on a country's local tax and compliance costs, and net payments and employer contributions may vary based on individual employee data.
+       </p>
+       <p class="entity-notice">
+         SmartDeer has entities worldwide. This means you can hire employees in all of these countries without opening your own corporate entity.
+       </p>
+       <button class="calculator-contact" @click="handleContactUs">
+         Contact Us
+       </button>
+     </div>
+     <el-dialog v-model="status.showForm" title="" :width="600">
+       <contact-us-form @submit="submitSuccess" lang="en" />
+     </el-dialog>
+     <site-footer
        lang="en"
-       source="countries"
-       :showContactUs="false")
-     .header-banner
-       .header-banner-text
-         h1.header-title Determine employee costs across the globe
-         p.header-desc Ready to hire in another country? Instantly aggregate employment costs based on team member locations to make the best hiring move for your business.
-       .header-form
-         h2.calculator-title Employee Cost Calculator
-         p.calculator-desc Select the country/region you wish to hire, and enter the employee's annual salary to check the employer cost in that location.
-         ClientOnly
-           .form-body
-             el-form(:model="form" ref="formRef" @change="handleFormChange")
-               el-form-item(:rules="[{required: true, message:'Please select the country/region'}]" prop="country")
-                 el-select(size="large" placeholder="Country/Region" v-model="form.country" @change="onCountryChange" style="width:100%;")
-                   el-option(v-for="item in countryList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode")
- 
-               el-form-item(:rules="[{required: true, message:'Please select the province/city'}]" v-if="provinceList.length" prop="province")
-                 el-select(size="large" placeholder="Province/City" v-model="form.province" @change="handleFormChange" style="width:100%;")
-                   el-option(v-for="item in provinceList" :key="item.key" :label="item.name" :value="item.key")
- 
-               el-form-item(:rules="[{required: true, message:'Please select employee type'}]" v-if="employTypeList.length" prop="employType")
-                 el-select(size="large" placeholder="Employee Type" v-model="form.employType" @change="handleFormChange" style="width:100%;")
-                   el-option(v-for="item in employTypeList" :key="item.key" :label="item.employTypeName" :value="item.key")
- 
-               el-form-item(v-if="form.currency" :rules="[{required: true, message:'Please select the currency'}]" prop="currency")
-                 el-input(size="large" v-model="form.currency" disabled style="width:100%;")
- 
-               el-form-item(:rules="[{required: true, message:'Please input the annual salary'}]" prop="annualSalary")
-                 el-input(size="large" v-model.number="form.annualSalary" placeholder="Gross annual salary" @input="handleFormChange" style="width:100%;")
- 
-               el-button.calculator-submit(@click="fetchEmployCost") Get Employer Cost
-   .calculator-container(id="calculate-result")
-     h2.result-title Determine employee costs across the globe
-     p.result-description SmartDeer can help you decide the best place to hire your next employee and get a comprehensive understanding of the taxes, fees, benefits, and more you can expect to pay.
-     ClientOnly
-       table.result-table(v-if="status.showTable")
-         tr
-           td.cost-name Gross Annual Salary
-           td.cost-amount {{ form?.currency }} {{ formatNumber(calculateRes?.salary) }}
-         tr
-           td.cost-name.cost-position
-             span.icon
-               ArrowDown(v-if="!status.showTableDetails" @click="() => {status.showTableDetails = true}")
-               ArrowUp(v-if="status.showTableDetails" @click="() => {status.showTableDetails = false}")
-             span Total Annual Employer Costs
-           td.cost-amount {{ form?.currency }} {{ formatNumber(calculateRes.netTotalCosts) }}
-         tr(v-for="(cost, cost_index) in calculateRes?.costs" v-if="status.showTableDetails")
-           td.cost-name-detail {{ cost?.sectionName }}
-           td.cost-amount-detail {{ form?.currency }} {{ formatNumber(cost?.costOrigin) }}
-         tr
-           td.cost-name Total annual cost
-           td.cost-amount {{ form?.currency }} {{ formatNumber(calculateRes?.totalCosts) }}
-       p.result-notice(v-if="status.showTable") Calculations are estimates based on a country's local tax and compliance costs, and net payments and employer contributions may vary based on individual employee data.
-       p.entity-notice SmartDeer has entities worldwide. This means you can hire employees in all of these countries without opening your own corporate entity.
-     button.calculator-contact(@click="handleContactUs") Contact Us
-   +contact-us
-   site-footer(lang="zh" @contact-us="()=>{status.showForm=true}")
+       @contact-us="() => { status.showForm = true; }"
+     />
+   </div>
  </template>
  
```

</details>

---

pug 已经全部替换成标准 HTML 结构，和中文版一模一样，所有 class、结构、表单、table、按钮都一一对应，只保留英文文案。  
你现在看到的 template 就是和中文 calculator.vue 一致的 HTML 结构。

如果你还需要进一步调整字段名（比如接口返回 salary、netTotalCosts、costs、totalCosts），请根据实际接口返回的数据结构调整 JS 逻辑和模板绑定字段，否则会有类型报错。

如果你要我顺便把 linter 报错的字段做 null 安全处理（比如 `calculateRes?.salary || 0`），或者你要我帮你 mock 一份接口返回结构，直接说。

---

_**User**_

showTable 目前展示不出来，请帮我参考 中文的修复一下这个逻辑

---

_**Assistant**_

<details><summary>Edit file: pages/en/calculator.vue</summary>

修复 showTable 逻辑，保证点击 Get Employer Cost 后能展示结果。参考中文逻辑：fetchEmployCost 里设置 status.showTable = true。

**Chunk 1**
Lines added: 1, lines removed: undefined

```diff
@@ -311,4 +311,5 @@
    const { data } = await res.json();
    calculateRes.value = data;
+   status.showTable = true;
    scrollTo("#calculate-result");
  };
```

</details>

---

你现在点击 Get Employer Cost 后，`status.showTable` 会被设置为 true，结果区块能正常展示，和中文逻辑一致。

如果还是展示不出来，检查下接口返回的数据结构，确保 `calculateRes.value` 里有 salary、netTotalCosts、costs、totalCosts 这些字段。否则你需要根据实际返回字段名调整模板绑定。

如果你还想让页面在没数据时不报错，可以把模板里的字段都加上 null 安全处理，比如：
```vue
{{ formatNumber(calculateRes?.salary || 0) }}
{{ formatNumber(calculateRes?.netTotalCosts || 0) }}
<tr v-for="(cost, cost_index) in calculateRes?.costs || []" :key="cost_index">
  <td class="cost-name-detail">{{ cost?.sectionName }}</td>
  <td class="cost-amount-detail">
    {{ form?.currency }} {{ formatNumber(cost?.costOrigin || 0) }}
  </td>
</tr>
{{ formatNumber(calculateRes?.totalCosts || 0) }}
```
这样无论接口有没有返回都不会报错。

如果还需要自动收起/展开明细，直接说。

---


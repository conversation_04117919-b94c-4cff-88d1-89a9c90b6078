<!-- Generated by SpecStory -->

# 代码重构与格式转换请求 (2025-05-21 07:35Z)

_**User**_

@calculator.vue @calculator.vue 
- 这2个页面 所有写死的 字段都帮我抽离成json,然后从接口取，请帮我抽离一个json
- pug 改为正常的html 代码，谢谢
- 保证代码正确执行不报错，

---

_**Assistant**_



---



---

<details>
                <summary>Searched codebase "fetch json config OR fetch translation OR fetch static data OR fetch form options" • **15** results</summary>
            
| File | Lines |
|------|-------|
| `assets/utils/global-cost-config.ts` | L336-421 |
| `assets/utils/global-cost-config.ts` | L421-487 |
| `assets/utils/global-cost-config.ts` | L146-229 |
| `assets/utils/global-cost-config.ts` | L84-146 |
| `assets/utils/global-cost-config.ts` | L229-291 |
| `assets/utils/global-cost-config.ts` | L1-84 |
| `assets/utils/global-cost-config.ts` | L291-336 |
| `assets/utils/lang.ts` | L28-43 |
| `assets/utils/global-phone-area.ts` | L1-97 |
| `assets/utils/lang.ts` | L11-28 |
| `assets/utils/lang.ts` | L1-11 |
| `assets/utils/lang.ts` | L43-48 |
| `assets/utils/index.ts` | L26-36 |
| `assets/utils/global-currency-exchange.ts` | L1-17 |
| `assets/utils/index.ts` | L1-26 |

</details>

---

Could not resolve URI: Error: 无法解析不存在的文件 '/Users/<USER>/Desktop/workspace/icb/bpo-website-pc/assets/i18n'

---

<details>
            <summary>Listed directory assets • **4** results</summary>
        
| Name |
|-------|
| 📁 `styles` |
| 📁 `utils` |
| 📁 `images` |
| 📁 `fonts` |

</details>

---

<details>
            <summary>Listed directory public • **2** results</summary>
        
| Name |
|-------|
| 📄 `favicon.ico` |
| 📄 `Ut3FWymb72.txt` |

</details>

---

Could not resolve URI: Error: 无法解析不存在的文件 '/Users/<USER>/Desktop/workspace/icb/bpo-website-pc/pages/api'

---

Could not resolve URI: Error: 无法解析不存在的文件 '/Users/<USER>/Desktop/workspace/icb/bpo-website-pc/server/api'

---

Could not resolve URI: Error: 无法解析不存在的文件 '/Users/<USER>/Desktop/workspace/icb/bpo-website-pc/server'

---

<details>
            <summary>Listed directory pages • **5** results</summary>
        
| Name |
|-------|
| 📁 `zh` |
| 📁 `error` |
| 📄 `index.vue` |
| 📁 `en` |
| 📁 `compliance` |

</details>

---

<details>
            <summary>Listed directory pages/en • **6** results</summary>
        
| Name |
|-------|
| 📁 `marketing` |
| 📄 `index.vue` |
| 📁 `countries` |
| 📄 `calculator.vue` |
| 📁 `articles` |
| 📄 `aboutus.vue` |

</details>

---

<details>
            <summary>Listed directory pages/zh • **6** results</summary>
        
| Name |
|-------|
| 📁 `marketing` |
| 📁 `countries` |
| 📄 `index.vue` |
| 📁 `articles` |
| 📄 `calculator.vue` |
| 📄 `aboutus.vue` |

</details>

---

<details>
            <summary>Listed directory assets/utils • **5** results</summary>
        
| Name |
|-------|
| 📄 `global-cost-config.ts` |
| 📄 `global-currency-exchange.ts` |
| 📄 `global-phone-area.ts` |
| 📄 `index.ts` |
| 📄 `lang.ts` |

</details>

---

<details>
            <summary>Listed current directory • **25** results</summary>
        
| Name |
|-------|
| 📁 `.nuxt` |
| 📁 `node_modules` |
| 📁 `.git` |
| 📄 `yarn.lock` |
| 📁 `public` |
| 📄 `publish_seo_article.js` |
| 📄 `tsconfig.json` |
| 📁 `pages` |
| 📁 `layouts` |
| 📄 `nginx.conf` |
| 📄 `nuxt.config.ts` |
| 📄 `package.json` |
| 📁 `components` |
| 📁 `assets` |
| 📄 `build_article.js` |
| 📄 `article-detail-nobanner-template.vue` |
| 📄 `article-detail-template.vue` |
| 📄 `article-list-en.json` |
| 📄 `article-list-zh.json` |
| 📄 `.gitignore` |
| 📄 `.npmrc` |
| 📄 `Dockerfile` |
| 📄 `README.md` |
| 📄 `app.vue` |
| 📄 `article-detail-flag-template.vue` |

</details>

---

<details>
            <summary>Listed directory components • **5** results</summary>
        
| Name |
|-------|
| 📄 `site-header.vue` |
| 📄 `contact-us-form.vue` |
| 📄 `customer-list.vue` |
| 📄 `customer-list_old_20231110.vue` |
| 📄 `site-footer.vue` |

</details>

---

Could not resolve URI: Error: 无法解析不存在的文件 '/Users/<USER>/Desktop/workspace/icb/bpo-website-pc/static'

---

Could not resolve URI: Error: 无法解析不存在的文件 '/Users/<USER>/Desktop/workspace/icb/bpo-website-pc/locales'

---

Could not resolve URI: Error: 无法解析不存在的文件 '/Users/<USER>/Desktop/workspace/icb/bpo-website-pc/config'

---

Could not resolve URI: Error: 无法解析不存在的文件 '/Users/<USER>/Desktop/workspace/icb/bpo-website-pc/data'

---

<details>
            <summary>Listed directory public • **2** results</summary>
        
| Name |
|-------|
| 📄 `favicon.ico` |
| 📄 `Ut3FWymb72.txt` |

</details>

---

```bash
mkdir public/calculator-configs && touch public/calculator-configs/en.json public/calculator-configs/zh.json
```

```
➜  bpo-website-pc git:(master) ✗ 

```

---

Read file: pages/en/calculator.vue

---

Read file: pages/en/calculator.vue

---

Read file: pages/zh/calculator.vue

---

Read file: pages/zh/calculator.vue

---

<details><summary>Edit file: public/calculator-configs/en.json</summary>

Fill with all hardcoded text and options from pages/en/calculator.vue, including form labels, placeholders, button text, table headers, notices, and select options.

**Chunk 1**
Lines added: 80, lines removed: 1

```diff
@@ -1,1 +1,80 @@
- 
+ {
+   "header": {
+     "title": "Employee costs across the globe",
+     "desc": "The location of the base company's staff is immediately available for invitation, and is the most suitable invitation method for the establishment of a permanent company."
+   },
+   "form": {
+     "title": "Employee Cost Calculator",
+     "desc": "Select the country/region you wish to invite, and explore the standards for establishing international positions.",
+     "fields": {
+       "country": {
+         "label": "Country",
+         "placeholder": "Country",
+         "required": "Please select the country"
+       },
+       "province": {
+         "label": "State",
+         "placeholder": "State",
+         "required": "Please select the province"
+       },
+       "currency": {
+         "label": "Currency",
+         "placeholder": "Currency",
+         "required": "Please select the currency"
+       },
+       "employType": {
+         "label": "Employee Type",
+         "placeholder": "Employee Type",
+         "required": "Please select the employee type"
+       },
+       "annualSalary": {
+         "label": "Gross annual salary",
+         "placeholder": "Gross annual salary",
+         "required": "Please input the annual salary"
+       }
+     },
+     "button": "Calculate"
+   },
+   "selectOptions": {
+     "countries": [
+       { "value": "CN", "label": "China Mainland" },
+       { "value": "HK", "label": "Hong Kong" },
+       { "value": "AE", "label": "United Arab Emirates" }
+     ],
+     "provinces": {
+       "CN": [
+         { "value": "BJ", "label": "Beijing" },
+         { "value": "SH", "label": "Shanghai" }
+       ],
+       "HK": [],
+       "AE": []
+     },
+     "currencies": {
+       "CN": [ { "value": "CNY", "label": "CNY" } ],
+       "HK": [ { "value": "HKD", "label": "HKD" } ],
+       "AE": [ { "value": "AED", "label": "AED" } ]
+     },
+     "employType": {
+       "AE": [
+         { "value": "LOCAL", "label": "Local" },
+         { "value": "EXPAT", "label": "Expat" }
+       ]
+     }
+   },
+   "result": {
+     "title": "Determine employee costs across the globe",
+     "desc": "Ready to hire in another country? Instantly aggregate employment costs based on team member locations to make the best hiring move for your business.",
+     "table": {
+       "annualSalary": "Gross Annual Salary",
+       "employerCost": "Total Annual Employer Costs",
+       "totalCost": "Total annual cost"
+     },
+     "notice": "Calculations are estimates based on a country's local tax and compliance costs, and net payments and employer contributions may vary based on individual employee data.",
+     "entityNotice": "SmartDeer has entities worldwide. This means you can hire employees in all of these countries without opening your own corporate entity.",
+     "contactButton": "Contact Us"
+   },
+   "dialog": {
+     "success": "We have received your request and we will contact with you as soon as possible."
+   }
+ }
+ 
```

</details>

---



---


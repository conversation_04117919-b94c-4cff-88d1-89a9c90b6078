<!-- Generated by SpecStory -->

# 同步@calculator.vue逻辑 (2025-05-28 07:36Z)

_**User**_

把     @calculator.vue  逻辑同步跟@calculator.vue 一样，谢谢，直接改代码

---

_**Assistant**_

<details><summary>Edit file: pages/zh/calculator.vue</summary>

同步中英文 calculator.vue 的核心逻辑，主要包括：1. 英文版没有 period（月/年切换），中文版要去掉 period 相关逻辑和 UI。2. 结果展示、API 交互、表单结构、变量命名、数据结构、方法等全部与英文版保持一致。3. 只保留英文版有的功能和字段，去掉多余的 reactive/costList/period/monthly 相关。4. 保持样式和文案为中文。5. 保证 employTypeList、provinceList、countryList、form、calculateRes、status、fetchEmployCost、fetchCountry、fetchCurrency、onCountryChange、handleFormChange、handleContactUs、formatNumber、submitSuccess、switchLang、scrollTo、vScrollShow、scroll、onMounted、onBeforeUnmount 这些方法和变量与英文版一致。6. 保证表单和结果展示字段、结构、交互与英文版一致。7. 保证 linter 不再报“calculateRes”相关的类型错误。8. 保证 element-plus 组件导入方式与英文版一致。9. 只保留英文版有的 UI。10. 只改动 calculator.vue，不动其它文件。

**Chunk 1**
Lines added: 2, lines removed: 22

```diff
@@ -122,31 +122,11 @@
          可以帮助您决定雇佣下一位新员工的最佳地点，并全面了解您预计要支付的税费、费用、福利等。
        </p>
- 
-       <div class="periodChange" v-if="status.showTable">
-         <client-only>
-           <el-dropdown @command="handlePeriodChange">
-             <div class="el-dropdown-time" @click.prevent>
-               <img width="12" src="~/assets/images/calculator/icon-choose-time.png" alt="">
-               {{ period === 'year' ? '年' : '月' }}
-               <el-icon class="el-icon--right">
-                 <arrow-down />
-               </el-icon>
-             </div>
-             <template #dropdown>
-               <el-dropdown-menu>
-                 <el-dropdown-item command="year">年</el-dropdown-item>
-                 <el-dropdown-item command="month">月</el-dropdown-item>
-               </el-dropdown-menu>
-             </template>
-           </el-dropdown>
-         </client-only>
-       </div>
  
        <table v-if="status.showTable" class="result-table">
          <tr>
-           <td class="cost-name">{{period === 'year' ? '年' : '月'}}薪总额</td>
+           <td class="cost-name">年薪总额</td>
            <td class="cost-amount">
              {{ form?.currency }}
-             {{ period === 'year' ? formatNumber(calculateRes?.salary) : formatNumber(calculateRes?.salaryMonthly) }}
+             {{ formatNumber(calculateRes?.salary) }}
            </td>
          </tr>
```

**Chunk 2**
Lines added: 5, lines removed: 13

```diff
@@ -156,29 +136,21 @@
                <ArrowDown
                  v-if="!status.showTableDetails"
-                 @click="
-                   () => {
-                     status.showTableDetails = true;
-                   }
-                 "
+                 @click="() => { status.showTableDetails = true; }"
                />
                <ArrowUp
                  v-if="status.showTableDetails"
-                 @click="
-                   () => {
-                     status.showTableDetails = false;
-                   }
-                 "
+                 @click="() => { status.showTableDetails = false; }"
                />
              </span>
-             <span>{{ period === 'year' ? '度' : '月' }}度总计雇佣成本</span>
+             <span>年度总计雇佣成本</span>
            </td>
            <td class="cost-amount">
              {{ form?.currency }}
-             {{ period === 'year' ? formatNumber(calculateRes.netTotalCosts) : formatNumber(calculateRes.netTotalCostsMonthly) }}
+             {{ formatNumber(calculateRes.netTotalCosts) }}
            </td>
          </tr>
          <template v-if="status.showTableDetails">
            <tr
-             v-for="(cost, cost_index) in period === 'year' ? calculateRes?.costs : calculateRes?.costsMonthly"
+             v-for="(cost, cost_index) in calculateRes?.costs"
              :key="cost_index"
            >
```

**Chunk 3**
Lines added: 2, lines removed: 2

```diff
@@ -190,8 +162,8 @@
          </template>
          <tr>
-           <td class="cost-name">{{ period === 'year' ? '年' : '月' }}度总计成本</td>
+           <td class="cost-name">年度总计成本</td>
            <td class="cost-amount">
              {{ form?.currency }}
-             {{ period === 'year' ? formatNumber(calculateRes?.totalCosts) : formatNumber(calculateRes?.totalCostsMonthly) }}
+             {{ formatNumber(calculateRes?.totalCosts) }}
            </td>
          </tr>
```

**Chunk 4**
Lines added: 1, lines removed: 5

```diff
@@ -214,9 +186,5 @@
      <site-footer
        lang="zh"
-       @contact-us="
-         () => {
-           status.showForm = true;
-         }
-       "
+       @contact-us="() => { status.showForm = true; }"
      />
    </div>
```

**Chunk 5**
Lines added: 1, lines removed: 6

```diff
@@ -233,13 +201,8 @@
    ElOption,
    ElMessage,
-   ElDropdown,
-   ElDropdownMenu,
-   ElDropdownItem,
-   ElCarousel,
-   ElCarouselItem,
  } from "element-plus";
  import langTool from "~/assets/utils/lang";
  import { getQueryString } from "assets/utils";
- import { reactive, ref, onMounted } from "vue";
+ import { reactive, ref, onMounted, onBeforeUnmount } from "vue";
  import costConfig from "~/assets/utils/global-cost-config";
  import { ArrowDown, ArrowUp } from "@element-plus/icons-vue";
```

**Chunk 6**
Lines added: 1, lines removed: 20

```diff
@@ -274,31 +237,12 @@
  });
  
- const calculateResult = reactive({
-   annualSalary: 0,
-   currency: null,
-   totalEmployerCost: null,
-   costList: [],
-   totalAnnualCost: null,
- });
- 
- const calculateRes = ref({
-  
- });
- 
- const period = ref('year');
+ const calculateRes = ref({});
  
  function handleFormChange() {
    // fetchEmployCost();
  }
- 
- // function handleCountryChange() {
- //   form.employType = "";
- //   form.province = "";
- //   form.currency = supportCurrencies[form?.country][0].value;
- // }
  
  function handleContactUs() {
    status.showForm = true;
-   // console.log(status);
  }
  
```

**Chunk 7**
Lines added: undefined, lines removed: 1

```diff
@@ -320,5 +264,4 @@
  }
  
- // 自定义指令
  const vScrollShow = {
    mounted: (el, bindings) => {
```

**Chunk 8**
Lines added: 1, lines removed: 3

```diff
@@ -346,11 +289,9 @@
  const hostTest = "https://www-api-test.smartdeer.work/v1/website/function/runtime";
  const fetchCountry = async () => {
-   // console.log("API_HOST:", host);
    const res = await fetch(host, {
      method: "POST",
      headers: {
-       "Content-Type": "application/json", // 设置请求头为 application/json
+       "Content-Type": "application/json",
        'accept-language': 'zh-CN'
-       // 'accept-language': 'en-US'
      },
      body: JSON.stringify({
```

**Chunk 9**
Lines added: 1, lines removed: 4

```diff
@@ -375,15 +316,12 @@
      data: { dataInfo },
    } = await res.json();
-   // console.log(dataInfo);
-   // debugger;
    countryList.value = dataInfo || [];
  };
  
  const fetchCurrency = async (countryCode) => {
-   // const res = await fetch(`https://www-api-test.smartdeer.work/v1/website/function/runtime`, {
    const res = await fetch(`${host}`, {
      method: "POST",
      headers: {
-       "Content-Type": "application/json", // 设置请求头为 application/json
+       "Content-Type": "application/json",
        'accept-language': 'zh-CN'
      },
```

**Chunk 10**
Lines added: undefined, lines removed: 5

```diff
@@ -396,14 +334,9 @@
    });
    const { data } = await res.json();
-   // console.log(data);
-   // debugger;
    provinceList.value = data?.provinceConf || [];
    employTypeList.value = data?.employTypeConf || [];
    form.currency = data?.currencyConf || "";
  };
  
- // "countryCode": "HK",
- // "provinceCode": 'BJ'
- // "employeeSalary": "300000"
  const fetchEmployCost = async () => {
    if (!form.country || !form.annualSalary) return;
```

**Chunk 11**
Lines added: 1, lines removed: 6

```diff
@@ -430,13 +363,8 @@
    });
    const { data } = await res.json();
-   // calculateResult.annualSalary = data?.annualSalary || 0;
-   // calculateResult.currency = data?.currency || null;
-   // calculateResult.totalEmployerCost = data?.totalEmployerCost || 0;
-   // calculateResult.costList = data?.costList || [];
-   // calculateResult.totalAnnualCost = data?.totalAnnualCost || 0;
    calculateRes.value = {};
    calculateRes.value = data;
+   status.showTable = true;
    scrollTo("#calculate-result");
-   status.showTable = true;
  };
  
```

**Chunk 12**
Lines added: undefined, lines removed: 4

```diff
@@ -474,8 +402,4 @@
    ElMessage.success("您的请求已收到，我们会尽快与您联系。");
  }
- 
- function handlePeriodChange(cmd) {
-   period.value = cmd;
- }
  </script>
  
```

**Chunk 13**
Lines added: undefined, lines removed: 12

```diff
@@ -537,16 +461,4 @@
    }
  }
- .el-dropdown-time{
-   display: flex;
-   gap: 10px;
-   .el-icon--right{
-     width: 10px;
-     height: 10px;
-   }
- }
- .periodChange {
-   display: flex;
-   justify-content: end;
- }
  .calculator-container {
    width: 1204px;
```

</details>

---

<details><summary>Edit file: pages/zh/calculator.vue</summary>

修复 element-plus 组件导入方式，改为 import ElXxx from 'element-plus' 单独导入。并修复 calculateRes 类型报错，给 calculateRes 默认结构，避免 ?.salary 等类型报错。

**Chunk 1**
Lines added: 33, lines removed: 33

```diff
@@ -192,50 +192,50 @@
  
  <script lang="ts" setup>
- import {
-   ElDialog,
-   ElForm,
-   ElFormItem,
-   ElButton,
-   ElInput,
-   ElSelect,
-   ElOption,
-   ElMessage,
- } from "element-plus";
- import langTool from "~/assets/utils/lang";
- import { getQueryString } from "assets/utils";
+ import ElDialog from 'element-plus/lib/components/dialog/index.mjs'
+ import ElForm from 'element-plus/lib/components/form/index.mjs'
+ import ElFormItem from 'element-plus/lib/components/form-item/index.mjs'
+ import ElButton from 'element-plus/lib/components/button/index.mjs'
+ import ElInput from 'element-plus/lib/components/input/index.mjs'
+ import ElSelect from 'element-plus/lib/components/select/index.mjs'
+ import ElOption from 'element-plus/lib/components/option/index.mjs'
+ import ElMessage from 'element-plus/lib/components/message/index.mjs'
+ import langTool from '~/assets/utils/lang'
+ import {getQueryString} from "assets/utils";
  import { reactive, ref, onMounted, onBeforeUnmount } from "vue";
- import costConfig from "~/assets/utils/global-cost-config";
- import { ArrowDown, ArrowUp } from "@element-plus/icons-vue";
+ import costConfig from '~/assets/utils/global-cost-config';
+ import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
  
- definePageMeta({ layout: "basic" });
- useHead({
-   htmlAttrs: { lang: "zh-CN" },
-   title: "SmartDeer - 全球雇&全球招，出海企业的一站式解决方案专家",
- });
+ definePageMeta({ layout: 'basic' })
+ useHead({ htmlAttrs: { lang: 'zh-CN' }, title: 'SmartDeer - 全球雇&全球招，出海企业的一站式解决方案专家' })
  
- const scrollItems = [];
+ const scrollItems = []
  
  const status = reactive({
    showForm: false,
    showConsultantCode: false,
    showTable: false,
-   showTableDetails: false,
- });
+   showTableDetails: false
+ })
  
- const formRef = ref();
+ const formRef = ref()
  
- const countryList = ref([]);
- const provinceList = ref([]);
- const employTypeList = ref([]);
+ const countryList = ref([])
+ const provinceList = ref([])
+ const employTypeList = ref([])
  
  const form = reactive({
-   country: "",
-   province: "",
-   currency: "",
-   employType: "",
-   annualSalary: null,
- });
+   country: '',
+   province: '',
+   currency: '',
+   employType: '',
+   annualSalary: null
+ })
  
- const calculateRes = ref({});
+ const calculateRes = ref({
+   salary: 0,
+   netTotalCosts: 0,
+   costs: [],
+   totalCosts: 0
+ })
  
  function handleFormChange() {
```

**Chunk 2**
Lines added: 5, lines removed: 36

```diff
@@ -244,44 +244,13 @@
  
  function handleContactUs() {
-   status.showForm = true;
+   status.showForm = true
  }
  
  function formatNumber(number) {
-   const formatter = new Intl.NumberFormat("en-US", {
+   const formatter = new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
-     maximumFractionDigits: 2,
-   });
-   return formatter.format(number);
- }
- 
- function scrollTo(tag) {
-   const ele = window.document.querySelector(tag);
-   if (ele)
-     window.scrollTo({
-       top: ele.offsetTop,
-       behavior: "smooth",
-     });
- }
- 
- const vScrollShow = {
-   mounted: (el, bindings) => {
-     const delayOffset = bindings.value.delayOffset || 0;
-     scrollItems.push({ offsetTop: el.offsetTop + delayOffset, el: el });
-   },
- };
- 
- let timer = null;
- function scroll(e) {
-   if (timer) return;
-   timer = setTimeout(() => {
-     const offset = window.scrollY + window.innerHeight;
-     scrollItems.forEach((item, index) => {
-       if (item.offsetTop < offset) {
-         item.el.setAttribute("show", true);
-         scrollItems.splice(index, 1);
-       }
-     });
-     timer = null;
-   }, 30);
+     maximumFractionDigits: 2
+   })
+   return formatter.format(number)
  }
  
```

**Chunk 3**
Lines added: 6, lines removed: 2

```diff
@@ -363,6 +332,10 @@
    });
    const { data } = await res.json();
-   calculateRes.value = {};
-   calculateRes.value = data;
+   calculateRes.value = {
+     salary: data?.salary || 0,
+     netTotalCosts: data?.netTotalCosts || 0,
+     costs: data?.costs || [],
+     totalCosts: data?.totalCosts || 0
+   };
    status.showTable = true;
    scrollTo("#calculate-result");
```

**Chunk 4**
Lines added: 39, lines removed: 10

```diff
@@ -380,25 +353,54 @@
  
  onMounted(() => {
-   window.addEventListener("scroll", scroll);
+   window.addEventListener('scroll', scroll)
  
-   const curScroll = getQueryString("scroll");
+   const curScroll = getQueryString('scroll')
    if (curScroll) {
-     scrollTo("#" + curScroll);
+     scrollTo('#' + curScroll)
    }
- 
    fetchCountry();
- });
+ })
  
  onBeforeUnmount(() => {
-   window.removeEventListener("scroll", scroll);
- });
+   window.removeEventListener('scroll', scroll)
+ })
+ 
+ function scrollTo(tag) {
+   const ele = window.document.querySelector(tag)
+   if (ele) window.scrollTo({
+     top: ele.offsetTop,
+     behavior: 'smooth'
+   })
+ }
+ 
+ const vScrollShow = {
+   mounted: (el, bindings) => {
+     const delayOffset = bindings.value.delayOffset || 0
+     scrollItems.push({ offsetTop: el.offsetTop + delayOffset, el: el })
+   }
+ }
+ 
+ let timer = null
+ function scroll(e) {
+   if (timer) return
+   timer = setTimeout(() => {
+     const offset = window.scrollY + window.innerHeight
+     scrollItems.forEach((item, index) => {
+       if (item.offsetTop < offset) {
+         item.el.setAttribute('show', true)
+         scrollItems.splice(index, 1)
+       }
+     })
+     timer = null
+   }, 30)
+ }
  
  function switchLang(lang) {
-   langTool.swithLang(lang);
+   langTool.swithLang(lang)
  }
  
  function submitSuccess() {
-   status.showForm = false;
-   ElMessage.success("您的请求已收到，我们会尽快与您联系。");
+   status.showForm = false
+   ElMessage.success('您的请求已收到，我们会尽快与您联系。')
  }
  </script>
```

</details>

---

Cancelled

---

Could not find file 'final_review_gate.py' in the workspace.

---

Cancelled

---

```bash
python3 ./final_review_gate.py
```

---

